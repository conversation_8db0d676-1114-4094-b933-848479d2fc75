# S参数仿真实验

以此前的 AC 交流小信号电路仿真为基础:

## 1. 电路各个元器件部分作用详细解读

![image-20250525162757569](https://raw.githubusercontent.com/jaynerlin/note_picture/master/20250525162757610.png)

1. **俩个电阻($R_1$和$R_2$)**:电压偏置电阻
2. **两个10pF电容($C_1$和$C_2$)**：用作输入和输出隔直电容.
3. **两个100nH电感($L_1$和$L_2$)**：射频扼流电感.

让我们来分析这些元器件的作用：

### 1.1 直流电压偏置电阻

​	作用：这两个电阻构成一个分压偏置网络 (Voltage Divider Bias)，其主要目的是为BJT晶体管 BJT1 的基极 (Base) 提供一个稳定且合适的直流偏置电压 ($V_B$)，并进而确定基极电流 ($I_B$) 和集电极电流 ($I_C$)，从而将晶体管的直流工作点 (Q-point) 设置在合适的放大区域（通常是正向放大区或有源区）。

### 1.2 隔直/耦合电容 (DC Blocking / Coupling Capacitors)

*   **功能与目的**:
    *   **隔离直流 (DC Blocking)**：电容器在直流下表现为开路。它们的主要作用是阻止直流电压和电流从电路的一部分传递到另一部分，同时允许交流信号通过。
    *   **耦合交流 (AC Coupling)**：在工作频率下，选择合适的电容值可以使其对交流信号呈现很低的阻抗（容抗 $X_C = 1 / (2\pi f C)$），从而有效地将交流信号从前一级耦合到后一级，或从信号源耦合到放大器输入，以及从放大器输出耦合到负载。

*   **具体应用**:
    *   **输入隔直电容 (Input DC Block)**：
        *   如图中的 $C_1$ 所示，它连接在信号源 (`Term1`) 和BJT基极之间。
        *   **作用**：
            1.  防止信号源的直流电平影响BJT基极的精心设计的直流偏置点。
            2.  防止BJT基极的直流偏置电压影响到信号源（如果信号源对直流敏感）。
    *   **输出隔直电容 (Output DC Block)**：
        *   如图中的$C_2$ 所示，它连接在BJT集电极和负载 (`Term2`) 之间。
        *   **作用**：
            1.  防止BJT集电极的直流偏置电压（通常较高）传递到负载或下一级电路，这可能会损坏负载或扰乱下一级的偏置。
            2.  防止负载的直流特性（如直流电阻）影响BJT集电极的直流工作点。

*   **取值 (10pF)**：电容值的选择取决于工作频率范围和与之相连的电路阻抗。在射频应用中，pF级别的电容通常足以在GHz频率下提供足够低的容抗，以实现良好的信号耦合。例如，在1GHz时，10pF的容抗约为15.9Ω，如果电路阻抗是50Ω，这个容抗相对较小，可以有效耦合信号。

### 1.3 射频扼流电感 (RF Choke Inductors)

将电感“与BJT的集电极和基极电阻一端相连”，目的是“将输出交流信号与两个极隔离开”。这里的“极”通常指直流电源点或偏置网络的连接点。这些电感用作**射频扼流圈 (RFC)**。

*   **功能与目的**:
    *   **提供直流通路 (DC Path)**：电感器对直流电流呈现很低的电阻，因此允许直流偏置电流顺利通过。
    *   **阻止交流信号 (AC Blocking/Choking)**：在工作频率下，选择合适的电感值可以使其对交流信号呈现很高的阻抗（感抗 $X_L = 2\pi f L$）。这可以阻止高频交流信号流入不希望它们进入的路径（如直流电源或偏置网络）。
*   **具体应用**:
    *   **基极偏置扼流 (Inductor for Base Bias Path)**：
        *   **连接方式**：通常串联在为基极提供直流偏置电流的路径上。例如，如果基极偏置电阻（如您图中的R1，或一个分压偏置网络中的电阻）从VCC电源获取电流，则可以在VCC到该电阻之间或电阻到基极的直流路径中加入扼流电感。
        *   **作用**：防止输入的交流信号通过基极偏置电阻泄漏到电源或其他偏置元件，从而确保更多的信号功率被有效地送到BJT的基极进行放大。它提高了输入信号的利用效率，并可能改善输入匹配。
    *   **集电极偏置扼流 (Inductor for Collector Bias Path)**：
        *   **连接方式**：通常串联在为集电极提供直流电流的路径上。例如，在集电极负载电阻（如图中的R2）与VCC电源的直流路径中串入一个扼流电感。
        *   **作用**:
            1.  防止经过放大的交流信号通过集电极负载电阻和电源内阻泄漏到VCC电源端，确保更多的信号功率被送到输出耦合电容和负载。
            2.  防止集电极的射频信号“污染”电源线，这可能会引起电路不稳定或干扰同一电源供电的其他电路。
            3.  有时，一个大的扼流电感可以直接用作集电极的交流负载（称为RFC负载），它可以提供很高的交流阻抗（从而获得高增益）同时允许直流电流通过。当此电路图中是与电阻连接，所以主要是扼流作用。
*   **取值 (100nH)**：电感值的选择同样取决于工作频率。在射频应用中，nH级别的电感可以在GHz频率下提供足够高的感抗。例如，在1GHz时，100nH的感抗约为628Ω，这个阻抗远大于典型的50Ω系统阻抗，可以有效地扼制射频信号。

#### 对比

未加射频扼流电感电路图仿真结果:
![image-20250525162324481](https://raw.githubusercontent.com/jaynerlin/note_picture/master/20250525162324540.png)

添加射频扼流电感电路图仿真结果:

![image-20250525163208628](https://raw.githubusercontent.com/jaynerlin/note_picture/master/20250525163208700.png)

> 可以发现添加了俩个100nH的扼流电感,增益从 8.210 提升到 8.494, 确保更多的信号功率被有效地送到BJT的基极进行放大。它提高了输入信号的利用效率,并且不影响或者会改善电路的阻抗匹配.

### 1.4 总结

*   **隔直电容**是为了确保电路中各级的**直流工作点相互独立且稳定**，同时保证**交流信号的有效耦合**。
*   **射频扼流电感**是为了给**直流偏置电流提供通路**，同时**阻止高频交流信号流入直流电源或偏置网络**，从而提高信号传输效率、防止电源污染和潜在的稳定性问题。

## 2. 史密斯圆解读示例

![image-20250525154148778](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250525154148778.png)

### 2.1 图上的关键信息：

1.  **标题/标签**:
    *   左侧纵轴标签 `S(1,1)` 表明图上绘制的曲线是端口1的反射系数 $S_{11}$。
    *   底部标签 `freq (100.0MHz to 5.000GHz)` 指出这条曲线是 $S_{11}$ 在频率从100MHz扫描到5GHz时的轨迹。

2.  **标记 (Marker) `m2`**:
    *   图上有一个标记 `m2` 指向曲线上的一个特定点。
    *   标记 `m2` 旁边显示了该点的数据：
        *   `freq=2.400GHz`: 该点对应的频率是2.4GHz。
        *   `S(1,1)=0.345 / -84.888`: 在2.4GHz时，$S_{11}$ 的值。
            *   `0.345` 是 $S_{11}$ 的**幅度 (magnitude)**，表示反射系数的大小。这个值介于0（完全匹配，无反射）和1（完全失配，全反射）之间。0.345意味着大约34.5%的入射电压波被反射回来了。
            *   `-84.888` 是 $S_{11}$ 的**相位 (phase)**，单位是度。
        *   `impedance = Z0 * (0.833 - j0.650)`: 在2.4GHz时，端口1的输入阻抗。
            *   `Z0` 是系统的参考特性阻抗 (characteristic impedance)，通常是50欧姆。
            *   `(0.833 - j0.650)` 是**归一化阻抗 (normalized impedance)**，即实际阻抗除以 `Z0`。
                *   `0.833` 是归一化电阻部分 (r)。
                *   `-j0.650` 是归一化电抗部分 (x)。负号表示容性电抗。
            *   所以，实际阻抗 $Z_{in} = Z0 \times (0.833 - j0.650)$。如果 $Z0 = 50\Omega$，则 $Z_{in} = 50 \times (0.833 - j0.650) = (41.65 - j32.5) \Omega$。

3.  **史密斯圆图的坐标系**:
    *   **圆心**: 史密斯圆图的圆心代表**匹配点**，即归一化阻抗为 $1+j0$ (即 $r=1, x=0$)，此时 $S_{11}$ 幅度为0。
    *   **水平直径 (实轴)**:
        *   从左到右代表纯电阻。最左端是0Ω（短路），圆心是Z0（匹配），最右端是无穷大Ω（开路）。
        *   上面的数字是归一化电阻值 (r)。
    *   **圆周上的弧线**:
        *   **等电阻圆 (Constant Resistance Circles)**: 这些是与水平实轴相切于最右端点（开路点）的一系列圆。每个圆上的所有点具有相同的归一化电阻值 (r)。您可以看到从0开始逐渐增大的r值对应的圆。
        *   **等电抗圆弧 (Constant Reactance Arcs)**: 这些是从最右端点（开路点）发出并终止于圆周边界的弧线。
            *   上半圆的弧线代表**感性电抗 (inductive reactance, +jx)**。
            *   下半圆的弧线代表**容性电抗 (capacitive reactance, -jx)**。
            *   水平实轴本身代表纯电阻，即电抗为0 (x=0)。
    *   **最外层圆周**:
        *   代表纯电抗（归一化电阻r=0）。
        *   也常用来读取反射系数的相位角（以度为单位，通常逆时针为正）。
    *   **径向刻度 (通常在图的下方或单独给出)**:
        *   用于读取反射系数的幅度 $|S_{11}|$。从圆心（幅度为0）向外到最外层圆周边界（幅度为1）。您的图中没有明确画出径向刻度尺，但可以通过点到圆心的距离来估算。

### 2.2 如何看懂图上的点 `m2` 和曲线：

1.  **定位 `m2`**:
    *   标记 `m2` 已经帮我们定位了2.4GHz时的 $S_{11}$。

2.  **读取 $S_{11}$ 幅度和相位**:
    *   **幅度 $|S_{11}| = 0.345$**: `m2` 点到史密斯圆图圆心的距离，如果用最外层圆周半径为1来归一化，这个距离就是0.345。
    *   **相位 $\angle S_{11} = -84.888^\circ$**: 如果从圆心向右画一条水平线（0度相位参考），然后从圆心画一条线到 `m2` 点，这两条线之间的夹角（通常按逆时针为正测量，或参考图外圈的相位刻度）就是相位。-84.888度意味着顺时针旋转约84.888度。

3.  **读取归一化阻抗**:
    *   标记 `m2` 处直接给出了归一化阻抗为 `0.833 - j0.650`。
    *   您也可以在图上估读：
        *   找到穿过 `m2` 点的**等电阻圆**。这个圆对应的r值大约是0.833。
        *   找到穿过 `m2` 点的**等电抗弧线**。这条弧线在下半圆，所以是容性的。它对应的x值（绝对值）大约是0.650，所以是-j0.650。

4.  **曲线的意义**:
    *   红色的曲线显示了从100MHz到5GHz，随着频率的增加，端口1的输入反射系数 $S_{11}$（也间接反映了输入阻抗）是如何在史密斯圆图上变化的。
    *   曲线的起点（100MHz）和终点（5GHz）可以通过观察曲线的端点来大致判断其位置。
    *   通过观察曲线离圆心的远近，可以了解在不同频率下的匹配程度。曲线越靠近圆心，匹配越好（反射越小）。

## 3. 理想匹配情况下S参数的表示

​	在射频和微波工程中，S参数（散射参数）是描述网络特性的关键指标，其中 $S_{11}$（输入反射系数）尤为重要，它表征了输入端口的匹配情况。理解理想匹配条件下 $S_{11}$ 如何表示，对于正确解读仿真与测量结果至关重要。

### 3.1 史密斯圆图上的表示

*   **圆心代表理想匹配**：史密斯圆图的**中心点**是完美匹配状态的图形表示。
*   **归一化阻抗**：在此中心点，端口的输入阻抗 $Z_{in}$ 等于系统的参考特性阻抗 $Z_0$。这意味着归一化阻抗 $z_{in} = Z_{in}/Z_0 = 1 + j0$。
*   **无反射**：当阻抗完美匹配时，所有入射到端口的能量都被负载吸收，没有能量被反射回来。

### 3.2 $S_{11}$ 的线性表示 (幅度和相位)

当达到理想匹配时，输入反射系数 $S_{11}$ 的线性表示如下：

*   **幅度 (Magnitude) $|S_{11}| = 0$**：
    这是理想匹配最直接的体现。由于没有能量反射，反射系数的幅度（即反射波电压与入射波电压之比的绝对值）为零。

*   **相位 (Phase) $\angle S_{11}$ = 未定义或约定为0度**：
    当一个复数的幅度为0时（例如复数 $0 + j0$），其相位在数学上是未定义的。在复平面上，幅度为0的点就是原点，它没有明确的方向。
    在工程实践中，当 $|S_{11}|=0$ 时：
    *   相位通常不再是关注的焦点。
    *   有时会约定俗成地将其表示为0度，例如 $S_{11} = 0 \angle 0^\circ$。
    重要的是理解，幅度为0是核心特征，此时的相位值没有实际物理意义。

因此，对于理想匹配，$S_{11}$ 可以简洁地表示为复数 $0$ (即 $0+j0$)。

### 3.3 $S_{11}$ 的dB表示

在射频领域，S参数常以分贝 (dB) 为单位表示，转换公式为：
$$
S_{dB} = 20 \times \log_{10}(|S|)
$$
对于理想匹配情况下的 $S_{11}$：

*   $|S_{11}| = 0$
*   因此，$S(1,1)_{dB} = 20 \times \log_{10}(0)$

由于对数函数 $\log_{10}(x)$ 在 $x \rightarrow 0^+$ 时趋向于负无穷大 ($-\infty$)，所以：

$$
S(1,1)_{dB} \rightarrow -\infty \text{ dB}
$$
这意味着理想匹配对应于一个无穷小的dB值（非常大的负数）。

### 3.4 实际考量

在实际的电路测量或仿真中，由于元器件的非理想性、制造容差、测量仪器的精度限制以及环境噪声等因素，几乎不可能达到绝对的 $|S_{11}| = 0$ 或 $S(1,1)_{dB} = -\infty$。

*   通常，一个“非常好”的匹配会被认为是 $S_{11}$ 的dB值非常负，例如：
    *   $-20 \text{ dB}$ 意味着反射功率是入射功率的1% ($|S_{11}| = 0.1$)。
    *   $-30 \text{ dB}$ 意味着反射功率是入射功率的0.1% ($|S_{11}| \approx 0.0316$)。
    *   $-40 \text{ dB}$ 意味着反射功率是入射功率的0.01% ($|S_{11}| = 0.01$)。

$S_{11}$ 的dB值越负，表明反射的信号越弱，端口的匹配程度越高。因此，在评估匹配性能时，我们通常追求尽可能低的 $S_{11}$ (dB) 值。

## 4. 阻抗匹配为何能消除反射

在射频、微波及高速数字电路领域，一个核心且至关重要的概念是**阻抗匹配**。当系统实现阻抗匹配时，一个显著的现象是所有入射到端口的能量都被负载完全吸收，几乎没有能量被反射回源端。这一特性不仅确保了能量传输的最高效率，也是保证信号完整性、避免设备损坏的关键。那么，这背后究竟是什么原理呢？

### 4.1 波的旅程：传输线与特性阻抗

想象一下电磁信号（如无线电波或高速数据脉冲）沿着特定的路径——传输线（例如同轴电缆或电路板上的微带线）传播。对于在其上传播的波而言，这条传输线展现出一种固有的“特性”，被称为**特性阻抗 ($Z_0$)**。

特性阻抗并非传统意义上的电阻，而是波在行进过程中“感受到”的瞬时电压与电流之比。它由传输线的物理构造（如导体的几何形状、尺寸、间距）以及构成线路的介质材料（如绝缘层的介电常数）共同决定。在一个均匀的、无限长的或末端完美匹配的传输线上，波会以恒定的电压电流比（即 $Z_0$）顺畅传播，仿佛在一条畅通无阻的高速公路上行驶。

### 4.2 终点遭遇：负载阻抗的挑战

当信号波沿传输线传播至末端时，它会遇到一个**负载**——可能是天线、另一个电路级、或是一个终端电阻。这个负载自身也具有阻抗，我们称之为**负载阻抗 ($Z_L$)**。

此时，波面临一个关键的“岔路口”：它所携带的能量是能被负载完全“接纳”，还是会因为“水土不服”而被部分或全部“弹回”？答案取决于负载阻抗 $Z_L$ 与传输线特性阻抗 $Z_0$ 之间的关系。

### 4.3 反射的产生：失配的后果

如果负载阻抗 $Z_L$ 与传输线的特性阻抗 $Z_0$ **不相等**（即 $Z_L \neq Z_0$），这种不匹配就会在传输线与负载的连接点（端口）处引起**反射**。

从物理上看，连接点处的电压和电流必须同时满足传输线（要求 $V/I = Z_0$）和负载（要求 $V/I = Z_L$）的约束。当 $Z_L \neq Z_0$ 时，这两个条件无法同时被一个单一的前向传播波满足。为了维持连接点处电压和电流的连续性以及它们与负载的关系，一部分入射波的能量不得不以反射波的形式向源端反向传播。

量化这种反射程度的参数是**反射系数 ($\Gamma$)**，它是一个复数，定义为：
$$
\Gamma = \frac{Z_L - Z_0}{Z_L + Z_0}
$$
反射系数的幅度 $|\Gamma|$ 表示反射波电压幅度与入射波电压幅度的比值，其范围从0（无反射）到1（全反射）。

### 4.4 阻抗匹配：当反射消失 ($\Gamma = 0$)

观察反射系数的公式，一个清晰的结论浮出水面：当且仅当**负载阻抗等于特性阻抗 ($Z_L = Z_0$)** 时，公式的分子 $(Z_L - Z_0)$ 变为零。这将导致：
$$
\Gamma = \frac{Z_0 - Z_0}{Z_0 + Z_0} = \frac{0}{2Z_0} = 0
$$
当反射系数 $\Gamma = 0$ 时，意味着反射波的幅度为零——即**没有反射波产生**。

​	根据能量守恒原理（假设传输线是无损耗的），入射到负载端口的能量只有两个去向：要么被负载吸收，要么被反射回源端。既然在阻抗匹配（$Z_L = Z_0$）的条件下，反射波的幅度为零，也就意味着没有能量被反射回来。

因此，所有入射的能量**必定完全被负载所吸收**。

#### 一个直观的类比

想象一下甩动一根长绳子产生一个波浪。
*   **不匹配**：如果绳子末端突然连接到一根非常轻或非常重的绳子，当波浪到达连接点时，你会看到一部分波浪传递过去，但很大部分会反射回来。
*   **匹配**：如果绳子末端连接到一根与原绳子特性完全相同的无限长绳子（或一个能完美吸收波浪能量的装置），当波浪到达“连接点”时，它会顺畅地继续前进，仿佛连接点不存在一样，所有能量都被“第二根绳子”吸收，不会有反射。

### 4.5 结论：阻抗匹配的重要性

阻抗匹配之所以能消除反射并确保能量完全被负载吸收，其核心在于它创造了一个“无缝”的过渡环境，使得传播的波在从传输线进入负载时，不会感受到阻抗的突变。这种平滑的过渡消除了产生反射的条件，从而允许能量高效、完整地传递。

在实际应用中，实现完美的阻抗匹配对于最大化功率传输、保证信号质量（避免由反射引起的失真和驻波）、防止高功率下对源端设备的损害以及优化系统整体性能都具有不可替代的重要性。

