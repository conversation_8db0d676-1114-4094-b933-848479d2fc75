## 1. 微带线(Microstrip)介绍

​		微带线是一种输电线路，可以用任何技术制造，其中导体通过称为衬底的介电层与接地层分开。微带线用于传输微波频率信号。微带线是平面传输线的多种形式之一，其他形式包括[带状线](https://en.wikipedia.org/wiki/Stripline)和[共面波导 ](https://en.wikipedia.org/wiki/Coplanar_waveguide)，并且可以将所有这些集成在同一衬底上。

​		典型的实现技术是印刷电路板（PCB）、涂有介电层的氧化铝，有时是硅或其他一些类似技术。微波元件，如天线、耦合器、滤波器、功率分配器等，都可以由微带线形成，整个器件作为基板上的金属化图案存在。因此，微带比传统的波导技术便宜得多，而且重量更轻、更紧凑。如下图所示

![image-20250528143138021](https://raw.githubusercontent.com/jaynerlin/note_picture/master/20250528143145110.png)

蓝色部分是导体，绿色部分是PCB的绝缘电介质，上面的蓝色小块儿是microstrip line。

![image-20250528143209896](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250528143209896.png)

其中黄色部分是环氧有机材料。

​		与波导相比，微带线的缺点是通常具有较低的功率处理能力和较高的损耗。此外，与波导不同，微带线通常不是封闭的，因此容易受到串扰和无意辐射的影响。

## 2. 耦合微带线滤波原理

​		耦合微带线滤波器是一种常见的微波滤波器，它利用平行放置的微带线之间的电磁耦合来实现滤波功能。其基本原理可以概括如下：

1.  **微带线 (Microstrip Line):** 微带线是一种传输线，由一个导体带（通常是铜）放置在一个介质基板的上方，而基板的另一面是接地面。信号沿着导体带传播。

2.  **耦合 (Coupling):** 当两条或多条微带线平行放置且彼此靠近时，它们之间会发生电磁场耦合。这意味着一条微带线上传播的电磁能量会部分地转移到相邻的微带线上。耦合的强度取决于微带线之间的距离、平行长度以及周围介质的特性。

3.  **谐振器 (Resonator):** 在耦合微带线滤波器中，通常会将微带线切割成特定长度的段，这些段可以作为谐振器。每个谐振器在其谐振频率附近对信号有较强的响应。谐振器的长度决定了其谐振频率。

4.  **滤波原理 (Filtering Principle):**
    *   **带通滤波器 (Bandpass Filter):** 这是耦合微带线滤波器最常见的应用。通过设计一系列特定长度和间距的耦合微带线谐振器，可以使得在特定频率范围（通带）内的信号能够有效地通过滤波器，而在此范围之外的频率（阻带）的信号则被显著衰减。
        *   输入信号进入第一个谐振器。
        *   能量通过耦合作用传递到相邻的谐振器。
        *   每个谐振器对特定频率的信号产生谐振，允许这些频率的信号通过。
        *   通过级联多个耦合谐振器，可以形成具有所需带宽和衰减特性的滤波器。谐振器的数量、长度、宽度以及它们之间的耦合间隙是设计的关键参数。
    *   **带阻滤波器 (Bandstop Filter):** 也可以设计耦合微带线结构来实现带阻滤波，即阻止特定频率范围内的信号通过，允许其他频率的信号通过。

5.  **关键设计参数:**
    *   **微带线长度和宽度:** 决定谐振频率和特性阻抗。
    *   **耦合间隙:** 控制谐振器之间的耦合强度，影响滤波器的带宽和插入损耗。
    *   **谐振器数量:** 影响滤波器的阶数，阶数越高，通带到阻带的过渡越陡峭，但插入损耗也可能增加。
    *   **介质基板的介电常数和厚度:** 影响微带线的特性和整体尺寸。

**总结来说，耦合微带线滤波器的工作原理是利用平行微带线段之间的电磁耦合，将这些线段设计成特定频率的谐振器。通过精确控制这些谐振器的尺寸、间距和数量，可以实现对信号频率的选择性通过或阻止，从而达到滤波的目的。**

