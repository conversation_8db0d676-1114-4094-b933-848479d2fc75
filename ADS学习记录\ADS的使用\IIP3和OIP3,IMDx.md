​	**可以通过 ADS 的help选项,弹出帮助窗口后搜索`Measurement Expressions`,可以知道所有ADS提供的函数作用和用法,每一个控件其实就是一个函数.**

在ADS仿真中计算衡量器件非线性度指标的各种谐波参数时,一般存在俩种方法:

```
方法一：使用HB分析结果

===== HB仿真基本设置 =====

必需控件：
1. P_nTone (Sources-Freq Domain)
   - 双音信号源：f1, f2 (如100MHz, 110MHz)
   - 功率设置：Pin1, Pin2 (通常相等)
   - 相位：通常设为0°

2. HB (Simulation-HB)  
   - 基波频率：选择f1或f2
   - 谐波次数：至少3次（用于IMD3分析）
   - 频率列表：[f1, f2]

3. 测量控件（可选）：
   - IP3out：自动计算OIP3
   - IP3in：自动计算IIP3  
   - MeasEqn：自定义计算
 
 ===== HB仿真下的计算方法 =====

// 在MeasEqn或数据显示中使用
IIP3_upper = ip3_out(vout, {1,0}, {2,-1}, 50)    // 上边频OIP3
IIP3_lower = ip3_out(vout, {1,0}, {-1,2}, 50)    // 下边频OIP3
OIP3_upper = ip3_in(vin, 0, {1,0}, {2,-1}, 50)   // 上边频IIP3
OIP3_lower = ip3_in(vin, 0, {1,0}, {-1,2}, 50)   // 下边频IIP3

方法1：直接功率提取
// 提取基波RMS电压
V_fund_f1 = vfc(vout, 0, {1,0})            // 100MHz基波RMS电压
V_fund_f2 = vfc(vout, 0, {0,1})            // 110MHz基波RMS电压
// 基波功率
P_fund_f1 = dBm(V_fund_f1, 50)         // 100MHz基波
P_fund_f2 = dBm(V_fund_f2, 50)         // 110MHz基波
// IMD3功率  (dBm)
P_imd3_lower = dBm(vfc(vout, 0, {2,-1}) , 50) // 90MHz
P_imd3_upper = dBm(vfc(vout, 0, {-1,2}) , 50) // 120MHz
// IMD3计算（相对载波dBc）
IMD3_lower = P_imd3_lower - P_fund_f1       // dBc
IMD3_upper = P_imd3_upper - P_fund_f2       // dBc

方法2：理论公式计算
// 使用OIP3计算IMD3
IMD3_upper = -2 * (OIP3_upper - P_fund_f2)
IMD3_lower = -2 * (OIP3_lower - P_fund_f1)
```

------

```
方法二：使用瞬态仿真分析结果

===== 瞬态仿真基本设置 =====

必需控件：
1. Vtsine (Sources-Time Domain)
   - 双音时域信号源
   - 频率：f1, f2
   - 幅度：V1, V2 (通常相等)
   - 相位：通常设为0°

2. Tran (Simulation-Tran)
   - 停止时间：足够长以达到稳态
   - 最大步长：满足最高频率采样要求
   - 典型设置：StopTime=200ns, MaxStep=100ps

3. 频谱分析控件：
   - VspecTran：电压频谱分析
   - PspecTran：功率频谱分析
   - 窗函数：通常选择Hanning或Blackman

典型瞬态设置示例：
StopTime = 200ns  // 至少包含20个最低频率周期
MaxStep = 100ps   // 最高频率的1/20
StartTime = 100ns // 跳过瞬态过程，只分析稳态

===== 瞬态仿真下的计算方法 =====

方法1：使用VspecTran控件
// 设置VspecTran控件
VspecTran1 = vspec_tran(vout, 0, freq_resolution, num_points)
// 计算频率索引
fund_index_f1 = round(f1 / freq_resolution) + 1
fund_index_f2 = round(f2 / freq_resolution) + 1  
imd3_upper_index = round((2*f2-f1) / freq_resolution) + 1
imd3_lower_index = round((2*f1-f2) / freq_resolution) + 1
// 提取功率
P_fund_f1 = dBm(VspecTran1[fund_index_f1], 50)
P_fund_f2 = dBm(VspecTran1[fund_index_f2], 50)
P_imd3_upper = dBm(VspecTran1[imd3_upper_index], 50)
P_imd3_lower = dBm(VspecTran1[imd3_lower_index], 50)
// 计算IMD3
IMD3_upper = P_imd3_upper - P_fund_f2
IMD3_lower = P_imd3_lower - P_fund_f1

方法2：使用PspecTran控件
// 直接计算功率谱
PspecTran1 = pspec_tran(vout, 0, 50, freq_resolution, num_points)
// 提取功率（已经是dBm）
P_fund_f1 = PspecTran1[fund_index_f1]
P_fund_f2 = PspecTran1[fund_index_f2]
P_imd3_upper = PspecTran1[imd3_upper_index]  
P_imd3_lower = PspecTran1[imd3_lower_index]
// 计算IMD3
IMD3_upper = P_imd3_upper - P_fund_f2
IMD3_lower = P_imd3_lower - P_fund_f1

方法3：时域功率计算
// 瞬时功率计算（需要电流信息）
p_inst = vout * I_Probe1.i
p_avg = time_avg(p_inst, start_time, stop_time)
p_avg_dBm = 10*log10(p_avg*1000)

方法4：使用测量函数
// 在MeasEqn中使用
// 注意：瞬态分析中不能直接使用ip3_out函数
// 需要先进行频谱分析，然后手动计算OIP3

// OIP3计算公式
P_out_avg = (P_fund_f1 + P_fund_f2) / 2
IMD3_avg = (IMD3_upper + IMD3_lower) / 2
OIP3_calc = P_out_avg - IMD3_avg / 2
```

> ​	对于 IMD3 计算 IMD3(dBc) = P_IMD3(dBm) - P_carrier(dBm),这里的 P_carrier应该是什么？对于IEEE标准：IEEE 1139-2008 (RF Amplifier Measurements),明确规定：使用频率最接近的单载波功率
>
> ​	对于IMD3的最终选取,一般IMD3_upper ≈ IMD3_lower,在 1.射频放大器：通常取最差值 2.混频器：可能指定特定频边 3.功率放大器：通常取最差值 4.学术论文：可能取平均值

## 最简谐波仿真

![image-20250722154624105](https://raw.githubusercontent.com/jaynerlin/note_picture/master/20250722154624201.png)

最简单的获取 IIPx/OIPx/IMDx 系数仿真,需要的控件为:

- Sources-Freq Domain板块 — P-nTone多频段信号输出控件
- Simulaton-HB板块 — HB谐波平衡分析控件(选择谐波基波频率和谐波次数)
- Simulaton-HB板块 — IP3out和OP3out调整函数表达式可以测量任意次数的谐波上下频边的交调点(可用公式ip3_out/ip3_in代替)

计算出输出交调点之后,就可以通过公式算出`IMDx = -2*(OIPx-Pout) `,单位dBc

**ip3_in函数语法：**
ip3_in(正端节点, 负端节点, 基波音调组合, IMD3音调组合, 参考阻抗)

> 例:IP3in1=ip3_in(vout,0,{1,0},{2,-1},50)
>
> - vout: 输出正端节点
> - 0: 输出负端节点(接地，单端输出)
> - {1,0}: 基波音调组合(第1个输入音调f₁)
> - {2,-1}: IMD3音调组合(2×f1-f2) **# 改变这个组合可以测试任意次数谐波的上下频带交调产物**
> - 50: 参考阻抗(50Ω)

**结果:**

![image-20250722154748576](https://raw.githubusercontent.com/jaynerlin/note_picture/master/20250722154748617.png)

> 可知 IMD3_lower 为-77.989dBm/-38.645dBc,或者直接用功率谱进行计算 -77.789-(-45.389)=32.4dBm

## 最简瞬态仿真