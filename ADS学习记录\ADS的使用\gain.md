## 最简

![](https://raw.githubusercontent.com/jaynerlin/note_picture/master/20250721172239023.png)

最简单的获取 gain 系数仿真,需要的控件为:

- Simulation-AC板块 — AC 信号频段步进控件(控制上图的 V_AC 模拟电压源输出的信号的频率)

**函数说明**

以下是ADS中MeasEqu控件常用的内置函数：

- **`mag(x)`** - 提取复数的幅度/模值
- **`phase(x)`** - 提取复数的相位（弧度）
- **`phase_deg(x)`** - 提取复数的相位（度）
- **`real(x)`** - 提取复数的实部
- **`imag(x)`** - 提取复数的虚部
- **`conj(x)`** - 复数共轭
- **`dB(x)`** - 转换为分贝 (20*log10(mag(x)))
- **`dBm(x)`** - 转换为dBm
- **`log10(x)`** - 常用对数（以10为底）
- **`ln(x)`** - 自然对数（以e为底）
- **`exp(x)`** - 指数函数 (e^x)
- **`sin(x)`** - 正弦函数
- **`cos(x)`** - 余弦函数
- **`tan(x)`** - 正切函数
- **`asin(x)`** - 反正弦函数
- **`acos(x)`** - 反余弦函数
- **`atan(x)`** - 反正切函数
- **`atan2(y,x)`** - 两参数反正切函数
- **`abs(x)`** - 绝对值
- **`sqrt(x)`** - 平方根
- **`pow(x,y)`** - 幂运算 (x^y)
- **`min(x,y)`** - 最小值
- **`max(x,y)`** - 最大值
- **`mean(x)`** - 平均值
- **`rms(x)`** - 均方根值
- **`sum(x)`** - 求和

在V_AC 控件中polar(1,0) mV 的含义：格式：polar(幅度, 相位)幅度：1 mV (毫伏), 相位：0° (度)

**结果:**

![](https://raw.githubusercontent.com/jaynerlin/note_picture/master/20250721174642308.png)

## 参数 VCC 扫描

![image-20250721174920824](https://raw.githubusercontent.com/jaynerlin/note_picture/master/20250721174920879.png)

观察不同的电源电压下输出增益的变化,新增控件:Simulation-AC板块 — Parameter sweep 控件,需要指定扫描变量 `SweepVar=VCC`并且指定初始值,结束值,步长;还需指定仿真实例为 AC1(AC控件的实例).

**结果:**

![image-20250721175300404](https://raw.githubusercontent.com/jaynerlin/note_picture/master/20250721175300441.png)
