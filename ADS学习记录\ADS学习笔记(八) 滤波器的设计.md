## 1．滤波器的逼近函数

​		滤波器在工程中使用相当普遍，一些数学逼近函数在滤波器设计中发挥着重要的作用。低通滤波器常见的逼近函数有**巴特沃斯函数（Butterworth）、切比雪夫函数（Chebyshev）、椭圆函数（Cauer）、贝塞尔函数（Bessel）以及高斯函数（Gaussian）**。通过这些逼近函数实现而成的滤波器分别称为巴特沃斯滤波器、切比雪夫滤波器、椭圆滤波器、贝塞尔滤波器和高斯滤波器。下面分别介绍这些滤波器各自的特点。

*   **巴特沃斯滤波器 (Butterworth Filter)**：
    *   其逼近函数为全极点系统，对于模拟滤波器，其极点均匀分布在S平面左半平面的一个圆上（其半径为截止频率）；对于数字滤波器，经过变换后极点会映射到Z平面的单位圆内。
    *   **特点**：通带内无纹波，具有**最大平坦幅度响应 (Maximally Flat Amplitude Response)**。这意味着其幅度特性在通带（尤其是在$\omega=0$处对于低通）尽可能平坦，前 $(2N-1)$ 阶导数为零。因此，“巴特沃斯逼近是最大平坦逼近”或者“巴特沃斯滤波器是最大平坦滤波器”是正确的说法，“最大平坦滤波器”常常就是指巴特沃斯滤波器。其幅频特性在通带与阻带内都呈单调变化。过渡带比较平缓，群延迟特性尚可，但不如贝塞尔滤波器。

*   **切比雪夫滤波器 (Chebyshev Filter)**：
    *   以牺牲通带或阻带的平坦度来换取更陡峭的过渡带。
    *   **I型切比雪夫滤波器 (Chebyshev Type I)**：
        *   逼近函数为全极点系统，其极点分布在S平面左半平面的一个椭圆上。
        *   **特点**：幅频特性在**通带内等波纹变化**，在阻带内单调衰减。其Q值相对较高。
    *   **II型切比雪夫滤波器 (Chebyshev Type II / Inverse Chebyshev)**：
        *   逼近函数具有零点与极点。对于模拟滤波器，极点分布不似I型那样规则，零点均位于虚轴之上。
        *   **特点**：幅频特性在**通带内单调衰减（通常是最大平坦的）**，在**阻带内等波纹变化**。
    *   **共性**：当逼近函数具有相同阶数时，切比雪夫滤波器（尤其是I型）比巴特沃斯滤波器有更好的衰减特性（更陡峭的过渡带）。

*   **椭圆滤波器 (Elliptic Filter / Cauer Filter)**：
    *   **特点**：在有限频率上既有零点又有极点。这些零点极点使得其幅频特性在**通带和阻带内均呈现等波纹特性**。阻带内的有限频率零点的存在使得其在阻带内获得极为陡峭的衰减特性曲线。
    *   **优势**：在所有具有相同阶数的滤波器中，椭圆滤波器能实现最窄的过渡带，或者说在同样的性能指标下，椭圆滤波器所需阶数最少。

*   **贝塞尔滤波器 (Bessel Filter / Thomson Filter)**：
    *   **特点**：频率选择性较差，其幅度滚降最为平缓。但是，它具有**最平坦的群延迟响应**，从而拥有近似线性的相位特性。因此有时又被称为**最大平坦延迟滤波器 (Maximally Flat Delay Filter)**。这使得它在时域上对脉冲信号的响应非常好，过冲和振铃很小。

*   **高斯滤波器 (Gaussian Filter)**：
    *   **特点**：
        *   其理想冲激响应是一个高斯函数。
        *   其阶跃响应具有**零过冲**的特性，并且对于给定的带宽，它拥有最快的上升时间（在无过冲的滤波器中）。
        *   时域和频域特性之间有良好的折衷，其传递函数的幅度响应也是高斯形状的（没有尖锐的截止）。
        *   频率选择性不如巴特沃斯或切比雪夫滤波器，过渡带非常平缓。
        *   相位响应接近线性，但不如贝塞尔滤波器平坦。
    *   **应用**：常用于需要良好脉冲响应、快速建立时间且无过冲的系统，如示波器、通信系统中的脉冲整形、以及图像处理中的平滑。

**各种滤波器的比较总结：**

*   **过渡带宽度 (同阶数下，从窄到宽)**：椭圆 > 切比雪夫I型 > 切比雪夫II型> 巴特沃斯 > 高斯 > 贝塞尔。
*   **阶数需求 (同样性能指标下，从少到多)**：椭圆 < 切比雪夫 < 巴特沃斯 < 贝塞尔/高斯。这意味着椭圆滤波器可以用最少的电路单元实现，节省功耗和芯片面积。
*   **时域响应 (从好到差，指过冲小、相位线性好)**：贝塞尔 > 高斯 > 巴特沃斯 > 切比雪夫 > 椭圆。
*   **实现复杂度与敏感性**：椭圆滤波器由于其极点品质因子 (Q值) 最大，所以在电路实现时对器件值的灵敏度很高，更容易因温度、工艺、电源电压的变化而与理想性能产生较大偏离。巴特沃斯和贝塞尔滤波器的Q值较低，相对不那么敏感。

选择哪种滤波器逼近函数取决于具体的工程应用需求，需要在频率选择性、相位线性度、时域响应、电路复杂度和元件敏感性等多个方面进行权衡。

## 2．滤波器的分类

按照构成滤波器元器件的性质、滤波器的功能和滤波器处理信号的形式，滤波器可以做如下不同的分类。

​		按照构成器件的性质分类，可以分成有源滤波器与无源滤波器。无源滤波器是指由电阻、电容和电感等无源器件组成的滤波器，无源滤波器噪声低、线性度高、工作频率高，但是电感集成占用很大芯片面积，而且高品质因数值的电感不易实现。有源滤波器通过使用有源器件构成的运算放大器，减小了所占用的芯片面积，并使得滤波器电路能够为有用信号提供一定增益，有利于整个系统灵敏度的提高。

​		按照滤波器的功能来分类，可以分为低通滤波器、高通滤波器、带通滤波器、带阻滤波器和全通滤波器。低通滤波器是指滤波允许低于截止频率的信号通过，而对高于截止频率*ω*0 的信号进行衰减与抑制。高通滤波器实现与低通滤波器完全相反的功能。高通滤波器对低于截止频率*ω*0 的信号进行衰减与抑制，而允许高于截止频率*ω*0 的信号顺利通过。带通滤波器可以认为是低通滤波器与高通滤波器所具有的性能的组合。带通滤波器允许某一个频带内[$w_L$，$w_H$ ]的信号通过，而对这个频段之外的信号进行衰减与抑制。

​		带阻滤波器是将某一个频段内的信号进行衰减与抑制，而允许该频段之外的信号通过。带阻滤波器如果阻带的比较狭窄，那么又可称为陷波器。

​		全通滤波器不具有频率选择性，理想的全通滤波器允许任何频率的信号通过。换言之，全通滤波器对幅度频率的响应是一条直线，增益为某一固定常数。信号通过全通滤波器时，其幅度不受影响，但是信号的相位会发生变化。全通滤波器主要运用于脉冲传输系统与延迟均衡器中。

## 3．滤波器的性能指标

​		在实际中，通常分别从以下两类指标来衡量模拟滤波器的性能。

- 第一类指标是用来表征滤波器的对相邻频道抑制干扰信号的抑制能力，包括截止频率和过渡带衰减速度等。
- 第二类指标是用来表征滤波器对系统的影响，包括带内纹波，带内群延迟变化，滤波器噪声以及线性度等。

带内纹波和群延时变化决定了信号从滤波器输入至滤波器输出的失真程度，带内纹波、群延迟会影响系统的矢量误差幅度，进而降低系统的误码率滤波器的噪声会影响到系统的信噪比。

