## 1. 三阶交调点坐标系图分析

![image-20250525224305947](https://raw.githubusercontent.com/jaynerlin/note_picture/master/20250525224305980.png)

我们来分析图1.5中“三阶交调点”坐标系图里的两条直线分别代表什么。

![image-20250525224547252](https://raw.githubusercontent.com/jaynerlin/note_picture/master/20250525224547274.png)

图中有两条向上倾斜的直线：

1.  **斜率较低的那条直线代表：基波输出功率 (Fundamental Output Power) 随输入功率的变化。**
    
    *   在理想的线性小信号工作区，输入功率每增加1dB，基波输出功率也应该增加1dB（假设增益是恒定的）。因此，这条线在对数坐标系（dBm是对数单位）下通常具有**斜率1**。

2.  **斜率较高的那条直线代表：三阶交调产物输出功率 (Third-Order Intermodulation Product Output Power) 随输入功率的变化。**
    
    *   当两个频率相近的信号（双音测试）输入到一个非线性器件时，会产生三阶交调产物。这些产物的频率通常非常接近原始输入信号的频率，因此很难通过滤波去除，是衡量线性度的重要指标。
    *   理论上，对于一个主要由三阶非线性主导的系统，当输入信号功率每增加1dB时，三阶交调产物的输出功率会增加**3dB**。因此，这条线在对数坐标系下通常具有**斜率3**。
    *   在图中，这条线从更低的位置开始（因为在小信号时，三阶交调产物非常弱），但以更陡峭的斜率向右上方延伸，表示其功率随输入功率的增加而迅速增长。
    
    > 基波幅度为 A, 三阶谐波分量的幅度为 $A^3$ ,各自取对数运算,所以三阶交调产物输出功率斜率是基波输出功率的三倍

**两条直线的交点：**

*   这两条直线的交点在理论上定义了**三阶截取点 (Third-Order Intercept Point, IP3)**。
    *   该交点处的**横坐标值**被称为**输入三阶截取点 (Input Third-Order Intercept Point, IIP3)**。它表示一个理论上的输入功率值，在该点，如果基波和三阶交调产物都继续按照其低功率时的斜率增长，它们的输出功率将会相等。
    *   该交点处的**纵坐标值**被称为**输出三阶截取点 (Output Third-Order Intercept Point, OIP3)**。它表示当输入功率达到IIP3时，基波（和三阶交调产物）的理论输出功率。

**需要注意的重要几点：**

*   **理论外推**：实际上，放大器在达到IP3点之前通常会进入饱和状态，增益会下降（基波输出功率的直线会弯曲向下），三阶交调产物的增长斜率也可能发生变化。因此，IP3点通常是一个通过测量小信号区域的数据并**外推**得到的理论点，而不是一个实际可达到的工作点。
*   **衡量线性度**：IIP3和OIP3的值越高，通常表示器件的线性度越好，即**==它可以在更高的输入功率下工作而不会产生过强的三阶交调失真==。**

所以，总结一下：

*   **斜率为1的直线**：表示**基波输出功率**随输入功率的变化。
*   **斜率为3的直线**：表示**三阶交调产物输出功率**随输入功率的变化。

这个图清晰地展示了为什么三阶交调产物对于线性度评估如此重要——它们的功率增长速度远快于基波，最终会限制系统的动态范围。

## 2. 输出三阶交调点分析控制器使用

![image-20250525223610900](https://raw.githubusercontent.com/jaynerlin/note_picture/master/20250525223610930.png)

​	`ipo1=ip3_out(vout,{1,0},{-1,2},50)` 是一个在ADS (Advanced Design System) 等电路仿真软件中常用的谐波平衡分析 (Harmonic Balance) 或电路包络 (Circuit Envelope) 分析后处理函数，用于计算**输出三阶截取点 (OIP3)**。

### 2.1 函数解读
我们来逐步解读这个函数及其参数的含义：

`ip3_out(Vout, Fund_idx, IM_idx, Z0)`

1.  **`ip3_out`**:
    *   这是函数名称，明确指示它计算的是**输出**三阶截取点 (Output IP3)。

2.  **`Vout`**:
    *   这是第一个参数，代表在仿真结果中需要进行OIP3计算的**输出节点电压**的名称。在您的例子中，输出节点被命名为 `vout`。
    *   仿真器会从这个节点提取基波和谐波/交调分量的电压或功率信息。

3.  **`{1,0}` (Fund_idx - 基波索引)**:
    *   这是一个指定**基波 (Fundamental)** 频率分量的索引。
    *   在谐波平衡分析中，通常会定义多个输入音调 (tones)。这个索引告诉函数哪个频率组合被视为主输出信号（即基波）。
    *   **`{1,0}` 的含义**：
        *   假设您进行了双音测试，输入了两个频率分别为 `freq[1]` (Tone 1) 和 `freq[2]` (Tone 2)。
        *   谐波平衡求解器会计算这些输入频率及其线性组合（谐波和交调产物）处的电压和电流。这些频率点可以用一组整数系数 $(m, n)$ 来表示，对应于 $m \cdot \text{freq}[1] + n \cdot \text{freq}[2]$。
        *   `{1,0}` 通常表示**第一个输入音调 ($1 \cdot \text{freq}[1] + 0 \cdot \text{freq}[2] = \text{freq}[1]$)**。这意味着我们关注的是第一个输入音调的输出功率作为基波。
        *   在某些情况下，如果只关心某个单一音调的输出（即使输入了双音以产生交调），这个参数就指定了那个音调。

4.  **`{-1,2}` (IM_idx - 交调产物索引)**:
    *   这是一个指定我们关心的**三阶交调产物 (Third-Order Intermodulation Product, IM3)** 的索引。
    *   对于双音输入 $f_1$ 和 $f_2$，主要的三阶交调产物出现在 $2f_1 - f_2$ 和 $2f_2 - f_1$。
    *   **`{-1,2}` 的含义**：
        *   这通常表示频率为 **$-1 \cdot \text{freq}[1] + 2 \cdot \text{freq}[2] = 2f_2 - f_1$** 的那个三阶交调产物。
        *   同样，`{2,-1}` 会表示 $2f_1 - f_2$。通常这两个IM3产物的功率非常接近，选择哪一个进行计算OIP3都可以，但需要保持一致性。
        *   仿真器会提取在这个特定频率组合下的输出功率。

5.  **`50` (Z0 - 参考阻抗)**:
    *   这是最后一个参数，代表系统的**参考阻抗**，单位是欧姆 (Ohms)。
    *   在射频和微波系统中，这个值通常是 **50Ω**。
    *   这个参数是必需的，因为OIP3通常是以功率单位 (如dBm) 表示的，而仿真器可能直接计算的是电压。为了从电压转换为功率，需要知道参考阻抗 ($P = V^2 / (2 \cdot Z0)$ 对于正弦波的RMS电压，或根据具体定义调整)。

### 2.2 函数的工作原理概要：

`ip3_out` 函数会执行以下操作（概念上）：

1.  从仿真结果中提取在 `Vout` 节点，由 `Fund_idx` 指定的基波频率的输出功率 ($P_{fund}$)。
2.  从仿真结果中提取在 `Vout` 节点，由 `IM_idx` 指定的三阶交调产物频率的输出功率 ($P_{IM3}$)。
3.  使用这两个功率值以及它们各自的增长斜率（基波功率随输入功率1:1增长，IM3功率随输入功率3:1增长，在dB尺度下）来外推计算OIP3点。
    *   我们知道在OIP3点，理论上基波输出功率等于IM3输出功率。
    *   如果在dBm单位下：
        $P_{fund}(dBm) = P_{in}(dBm) + Gain(dB)$
        $P_{IM3}(dBm) = 3 \cdot P_{in}(dBm) + Constant_{IM3}$ (这里的Constant包含了器件的非线性系数等)
    *   OIP3点可以通过找到当 $P_{fund}$ 外推线与 $P_{IM3}$ 外推线的交点来确定。
    *   一个常用的计算公式（基于功率差）：
        $OIP3 (dBm) = P_{fund} (dBm) + \frac{P_{fund} (dBm) - P_{IM3} (dBm)}{2}$
        或者等效地：
        $OIP3 (dBm) = P_{IM3} (dBm) + \frac{P_{fund} (dBm) - P_{IM3} (dBm)}{3-1} \times 3 = P_{IM3} (dBm) + \frac{3}{2} (P_{fund} (dBm) - P_{IM3} (dBm))$
        (注意：上面两个简化公式仅在特定条件下（例如，基波增益为0dB或IM3的定义方式）才完全等效，ADS内部可能有更精确的基于斜率的计算。)
    *   ADS软件内部会使用更精确的、基于外推的算法。它会利用已知的功率点和理论斜率来计算交点。

### 2.3 总结

`ipo1=ip3_out(vout,{1,0},{-1,2},50)` 这个表达式的含义是：

**计算输出节点 `vout` 处的输出三阶截取点 (OIP3)，其中基波被定义为输入音调组合 `{1,0}` (即第一个输入音调 $f_1$) 的输出，而用于计算的三阶交调产物被定义为输入音调组合 `{-1,2}` (即 $2f_2 - f_1$) 的输出。计算OIP3时使用的系统参考阻抗是50Ω。变量 `ipo1` 将被赋予计算得到的OIP3值（通常以dBm为单位）。**



## 3.如何计算三阶交调点(OIP3,IIP3)

![image-20250525230131320](https://raw.githubusercontent.com/jaynerlin/note_picture/master/20250525230131375.png)

我们来根据如上所示的频谱图中的标记 (m1, m2, m3, m4) 数据，计算输出三阶截取点 (OIP3)。通常，上边频和下边频的OIP3值会非常接近，我们分别计算一下。

**基本数据：**

*   **基波音调1 (m1):**
    *   频率 ($f_1$) = 2.395 GHz
    *   输出功率 ($P_{f1}$) = -19.004 dBm
*   **基波音调2 (m2):**
    *   频率 ($f_2$) = 2.405 GHz
    *   输出功率 ($P_{f2}$) = -19.028 dBm
*   **下三阶交调产物 (m3, Lower IM3):**
    *   频率 ($f_{IM3L}$) = 2.385 GHz (即 $2f_1 - f_2 = 2 \times 2.395 - 2.405 = 4.790 - 2.405 = 2.385$)
    *   输出功率 ($P_{IM3L}$) = -69.343 dBm
*   **上三阶交调产物 (m4, Upper IM3):**
    *   频率 ($f_{IM3U}$) = 2.415 GHz (即 $2f_2 - f_1 = 2 \times 2.405 - 2.395 = 4.810 - 2.395 = 2.415$)
    *   输出功率 ($P_{IM3U}$) = -74.476 dBm

### 3.1 计算输出三阶交调点(OIP3)
**OIP3 计算公式：**

三阶输出截取点 (OIP3) 可以使用以下常用公式进行估算（基于功率差）：
$$
OIP3 (\text{dBm}) = P_{fund} (\text{dBm}) + \frac{\Delta P (\text{dB})}{2}
$$
或者更准确地写为：

$$
OIP3 (\text{dBm}) = P_{fund} (\text{dBm}) + \frac{P_{fund} (\text{dBm}) - P_{IM3} (\text{dBm})}{2}
$$
其中：
*   $P_{fund}$ 是其中一个基波音调的输出功率 (dBm)。如果两个基波功率略有不同，通常取两者的平均值，或者分别计算然后看是否接近。为了与表格中的 `ipo1` 和 `ipo2` 对应，我们会分别用 $P_{f1}$ 和 $P_{f2}$ 作为参考。
*   $P_{IM3}$ 是对应于所选基波的那个三阶交调产物的输出功率 (dBm)。

**1. 计算 ipo1 (对应下边频三阶交调点，即参考 $m4$ 和 $m2$)**

这里“上边频三阶交调点”指的是基于 $2f_2 - f_1$ (m4) 这个交调产物计算得到的OIP3。我们通常会用离它“更近”的基波，或者说，它是通过 $f_2$ 的二次谐波与 $f_1$ 混频产生的，所以 $f_2$ (m2) 是一个合适的参考基波。

$ipo1 = P_{f2} + \frac{P_{f2} - P_{IM3U}}{2}$
$ipo1 = -19.028 \text{ dBm} + \frac{(-19.028 \text{ dBm}) - (-74.476 \text{ dBm})}{2}$
$ipo1 = -19.028 + \frac{-19.028 + 74.476}{2}$
$ipo1 = -19.028 + \frac{55.448}{2}$
$ipo1 = -19.028 + 27.724$
$ipo1 = 8.696 \text{ dBm}$

**2. 计算 ipo2 (对应上边频三阶交调点，即参考 $m3$ 和 $m1$)**

这里“下边频三阶交调点”指的是基于 $2f_1 - f_2$ (m3) 这个交调产物计算得到的OIP3。它是通过 $f_1$ 的二次谐波与 $f_2$ 混频产生的，所以 $f_1$ (m1) 是一个合适的参考基波。

$ipo2 = P_{f1} + \frac{P_{f1} - P_{IM3L}}{2}$
$ipo2 = -19.004 \text{ dBm} + \frac{(-19.004 \text{ dBm}) - (-69.343 \text{ dBm})}{2}$
$ipo2 = -19.004 + \frac{-19.004 + 69.343}{2}$
$ipo2 = -19.004 + \frac{50.339}{2}$
$ipo2 = -19.004 + 25.1695$
$ipo2 = 6.1655 \text{ dBm}$

**结果对比与表格：**

*   **计算得到的 ipo1 (上边频 OIP3) = 8.696 dBm**
*   **计算得到的 ipo2 (下边频 OIP3) = 6.1655 dBm**

**表格中的值：**

*   **ipo1 = 8.696**
*   **ipo2 = 6.130**

可以发现基本相同,接下来我们通过输出三阶交调点除以增益得到输入三阶交调点(假设增益 (Gain) = $11.031 \text{ dB}$)

### 3.2 计算输入三阶交调点(IIP3)

**计算输入三阶交调点 (IIP3) 的公式：**

IIP3, OIP3 和增益 (Gain) 之间的关系（均以dB或dBm为单位）是：
$$
OIP3 (\text{dBm}) = IIP3 (\text{dBm}) + Gain (\text{dB})
$$
所以，我们得到了两个IIP3值：
*   **$IIP3_1 \approx -4.901 \text{ dBm}$** (基于下边频交调产物)
*   **$IIP3_2 \approx -2.335 \text{ dBm}$** (基于上边频交调产物)

在实际评估时，通常会关注两者中较差的（即较低的）那个值，或者将两者都列出。

**IIP3 的含义以及与线性放大结果的关系：**

​	计算得到的IIP3值（例如，我们取较差的那个，约为-4.901 dBm）提供了一个线性度性能的基准。当实际的输入信号功率远低于这个IIP3值时，放大器的三阶交调失真会很小，从而能够提供较好的线性放大效果。如果输入功率接近或超过IIP3（理论上），则非线性失真将非常严重，放大器不再处于线性工作区。

### 3.3 总结

- **IIP3 的值越高，表明放大器的线性度越好。** 这意味着放大器可以在更高的输入功率水平下工作，而产生的三阶交调失真相对较小。
- **当输入信号的功率远低于IP3时**，三阶交调产物的功率相对于基波信号来说会非常低。这意味着放大器对信号的处理接近理想线性状态，失真很小，因此“拥有较好的线性放大结果”。

*   **通常，为了保证良好的线性度，实际工作时的输入功率应比IIP3低很多。** 一个常见的经验法则是，为了使三阶交调产物被充分抑制（例如，比主信号低30dBc, 40dBc或更多），输入功率可能需要比IIP3低10dB到20dB，甚至更多，具体取决于应用对线性度的要求。