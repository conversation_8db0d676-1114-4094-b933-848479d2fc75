当采样率不满足奈奎斯特采样定理（即采样率 $f_s$ 小于信号最高频率 $f_{max}$ 的两倍，$f_s < 2f_{max}$）时，会导致频谱混叠 (Aliasing)。频谱混叠是指原始信号中高于奈奎斯特频率 ($f_s/2$) 的频率分量在采样后会“折叠”或“混淆”到低于奈奎斯特频率的频带内。

这种频谱上的混叠在**时域上**的表现主要是：

1.  **波形失真 (Waveform Distortion)**：
    *   由于高频分量被错误地表示为低频分量，重建后的模拟信号（如果通过DAC转换回去）其**波形形状将与原始模拟信号的真实波形完全不同**。
    *   原始信号中的快速变化或高频细节会丢失，取而代之的是一些错误的、看起来更缓慢变化的成分。

2.  **产生虚假的低频成分 (Appearance of Spurious Low Frequencies)**：
    *   在时域上，你会观察到一些**原本不存在于原始信号中的低频振荡或模式**。这些是由于原始信号的高频部分混叠到低频区域造成的。
    *   例如，一个快速振荡的高频正弦波，如果采样率过低，重建后在时域上可能表现为一个缓慢振荡的、频率完全不同的正弦波。你本来看不到这个慢速振荡，但混叠把它“制造”出来了。

3.  **信息丢失 (Loss of Information)**：
    *   原始信号中高于奈奎斯特频率的信息在采样过程中丢失了，并且无法通过采样后的数据点准确恢复。时域上表现为**无法再现原始信号的真实动态和细节**。

4.  **无法唯一确定原始信号**：
    *   这是混叠的根本问题。在时域上，这意味着有多个不同的原始模拟信号（包含不同高频成分）在经过欠采样后，会产生完全相同的离散采样点序列。因此，仅凭这些采样点，你无法判断原始信号到底是哪一个。

**举例说明：**

*   **简单的正弦波**：假设你有一个9kHz的正弦波，但你用了10kHz的采样率。奈奎斯特频率是5kHz。9kHz的信号会混叠成 $|9kHz - 10kHz| = |-1kHz| = 1kHz$ 的信号。那么在时域上，你本来期望看到每秒振荡9000次的波形，但采样后再重建，你看到的会是一个每秒振荡1000次的波形。这两种波形在示波器上看起来是完全不一样的。

*   **音频信号**：如果对包含高频泛音的音乐信号进行欠采样，混叠会使这些高频泛音变成刺耳的、不和谐的低频噪声或啸叫，在时域上，原始清晰的乐器声波形会被这些混叠进来的杂乱波形所污染。

*   **视觉上的例子（时间采样）**：著名的“马车轮效应”，即在电影或视频中，快速旋转的车轮有时看起来转得很慢，甚至倒转。这是因为视频的帧率（时间采样率）不足以捕捉车轮辐条的快速运动，导致运动频率发生混叠。虽然这不是电压随时间变化的信号，但原理是相似的。

**总结来说，频谱混叠在时域上的表现就是重建信号与原始信号在波形形状、频率成分和动态细节上的显著差异和失真，出现了本不该有的低频特征，同时丢失了真实的高频信息。**