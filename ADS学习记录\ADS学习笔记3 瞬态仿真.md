## 瞬态仿真放大倍数与交流仿真不一致
为什么对同一个BJT电路进行交流信号仿真和进行瞬态仿真,得出交流信号仿真的放大倍数是3.422,但是瞬态仿真放大倍数只有3.1(如下图所示)?

电路图:
![image-20250525204238011](https://raw.githubusercontent.com/jaynerlin/note_picture/master/20250525204238046.png)

仿真结果

![image-20250525204721373](https://raw.githubusercontent.com/jaynerlin/note_picture/master/20250525204721429.png)

## 原因

其主要原因通常归结于这两种仿真方法的根本区别以及电路的非线性特性：

1.  **交流小信号分析 (AC Analysis) 的线性化假设：**
    *   **小信号模型**：AC分析是在电路的直流工作点 (DC Operating Point) 附近进行的一种**线性化**分析。它首先计算出电路中所有非线性器件（如BJT）的直流偏置状态，然后用它们在该工作点附近的**小信号等效模型**替代这些器件。例如，BJT会被替换为一个由跨导 ($g_m$)、输入电阻 ($r_{\pi}$)、输出电阻 ($r_o$) 等线性元件组成的模型。
    *   **无限小信号**：AC分析理论上假设所施加的交流信号是**无限小的 (infinitesimal)**，因此器件的响应完全是线性的，不会产生谐波失真，增益在该工作点是一个确定的值。图右中的 `gain_main = 3.422` 就是在这种理想线性小信号条件下的增益。

2.  **瞬态分析 (Transient Analysis) 的非线性行为：**
    *   **完整非线性模型**：瞬态分析则是随时间求解电路的非线性微分方程。它使用器件（如BJT）的**完整非线性大信号模型**。这意味着它会考虑器件在实际信号幅度下的所有非线性效应。
    *   **实际信号幅度**：在上面的瞬态仿真中，输入信号 `vin` 的幅度是 `Amplitude=10 mV`。虽然10mV在某些情况下可能被认为是“小信号”，但对于BJT的指数型 $I_C-V_{BE}$ 特性来说，这个幅度的信号可能已经足以让晶体管展现出一定的非线性行为。
        *   从瞬态仿真图（图左）来看，输入信号 `vin` (红色波形) 峰值是10mV，输出信号 `vout` (蓝色波形) 峰值大约是31mV (根据标记 `m1: vout=0.031`，即31mV)。
        *   因此，瞬态仿真中观察到的电压放大倍数约为 $31mV / 10mV = 3.1$。

3.  **非线性导致的增益差异：**
    *   **增益压缩/扩展**：当实际信号幅度（瞬态仿真中的10mV）驱动BJT时，其有效跨导 ($g_m$) 可能不再是直流工作点处计算得到的那个恒定的小信号值。如果信号摆幅使得BJT进入轻微的饱和区或截止区边缘，或者仅仅是由于其 $I_C-V_{BE}$ 曲线的非线性，都可能导致**增益压缩 (gain compression)**，即实际大信号下的增益低于理想小信号增益。反之，也可能出现增益扩展，但增益压缩在放大器中更常见，尤其是在输出摆幅接近电源轨或输入信号较大时。
    *   **谐波失真**：非线性还会导致输出信号中产生谐波分量。AC分析只关注基波频率的响应。瞬态仿真如果简单地通过峰值或峰峰值来计算增益，而输出波形由于失真其峰值并不完全由基波贡献，也可能导致与小信号增益的差异。不过从图右的波形看，`vout` 看起来还是比较标准的正弦波，所以是可以排除这个原因.


**总结来说，最主要的原因是：**

*   **AC仿真是基于理想线性小信号模型的计算结果。**
*   **瞬态仿真是基于实际信号幅度下器件完整非线性模型的计算结果。**

当瞬态仿真中的输入信号幅度（即使是10mV）足以使BJT表现出非线性时，其“大信号”或“实际信号”下的增益就会偏离纯粹的“小信号”增益。观察到的从3.422下降到3.1，很可能是由于10mV的输入信号在1GHz下对BJT产生了一定程度的增益压缩。

## 验证方法：

如果想验证这一点，可以在瞬态仿真中尝试使用更小的输入信号幅度，例如1mV甚至0.1mV。如果随着输入幅度的减小，瞬态仿真得到的增益逐渐接近AC分析的3.422，那么就印证了非线性是主要原因。

![image-20250525161020171](https://raw.githubusercontent.com/jaynerlin/note_picture/master/20250525161020215.png)