```mermaid
flowchart TD
    A[射频放大器故障分类] --> B[硬故障 Hard Faults]
    A --> C[软故障 Soft Faults]
    
    B --> B1[行为级故障]
    B --> B2[晶体管级故障]
    B --> B3[工艺故障]
    
    B1 --> B11["• 失调电压过大<br/>• 增益异常<br/>• 输出饱和削波<br/>• 阻抗失配"]
    
    B2 --> B21["• 开路故障<br/>• 短路故障<br/>• 电容故障"]
    
    B3 --> B31["• 金属迹线断裂<br/>• 通孔不完全<br/>• 寄生电容<br/>• 弱开路缺陷"]
    
    C --> C1["• 电阻漂移 ±10%<br/>• 电容漂移 ±15%<br/>• 电感漂移 ±8%<br/>• 多参数协同漂移"]
    
    classDef rootNode fill:#e3f2fd,stroke:#1976d2,stroke-width:3px,color:#000
    classDef hardFault fill:#ffebee,stroke:#d32f2f,stroke-width:2px,color:#000
    classDef softFault fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    classDef subCategory fill:#fff8e1,stroke:#f57c00,stroke-width:2px,color:#000
    classDef details fill:#f1f8e9,stroke:#388e3c,stroke-width:1px,color:#000
    
    class A rootNode
    class B hardFault
    class C softFault
    class B1,B2,B3 subCategory
    class B11,B21,B31,C1 details
```









```mermaid
flowchart TD
    A[射频放大器故障检测项目] --> B[第一阶段: 基础建模]
    A --> C[第二阶段: 故障注入]
    A --> D[第三阶段: 智能分类]
    A --> E[第四阶段: 验证应用]
    
    B --> B1["SPICE模型建立<br/>• LMH6881精确建模<br/>• 关键参数验证<br/>• 仿真环境搭建"]
    
    C --> C1["硬故障注入<br/>• 行为级故障模拟<br/>• 晶体管级故障<br/>• 工艺故障建模"]
    C --> C2["软故障注入<br/>• 蒙特卡洛仿真<br/>• 参数容差调整<br/>• 批量数据生成"]
    
    D --> D1["数据预处理<br/>• 时域波形提取<br/>• 特征工程<br/>• 数据标准化"]
    D --> D2["CNN模型训练<br/>• 网络架构设计<br/>• 超参数优化<br/>• 性能评估"]
    
    E --> E1["模型验证<br/>• 多芯片测试<br/>• 迁移能力评估<br/>• 性能指标分析"]
    E --> E2["实际应用<br/>• 硬件平台搭建<br/>• 实物测试验证<br/>• 工程化部署"]
    
    B1 --> F[数据采集]
    C1 --> F
    C2 --> F
    F --> D1
    D1 --> D2
    D2 --> E1
    E1 --> E2
    
    classDef stage fill:#e3f2fd,stroke:#1565c0,stroke-width:3px,color:#000
    classDef task fill:#f1f8e9,stroke:#2e7d32,stroke-width:2px,color:#000
    classDef process fill:#fff8e1,stroke:#ef6c00,stroke-width:2px,color:#000
    classDef flow fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    
    class A,B,C,D,E stage
    class B1,C1,C2,D1,D2,E1,E2 task
    class F flow
```

