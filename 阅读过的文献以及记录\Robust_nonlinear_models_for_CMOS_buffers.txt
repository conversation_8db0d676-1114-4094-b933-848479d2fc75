Robust nonlinear models for CMOS buffers 

C. Diouf 

École Nationale d’Ingénieurs de 

Brest 

CNRS UMR 6285 Lab-STICC 

Brest, France 

M. <PERSON>, N. Tanguy 

Université de Bretagne Occidentale 

CNRS UMR 6285 Lab-STICC 

Brest, France 

<EMAIL> 

I. <PERSON>. <PERSON>, F. G. Canavero 

Dept. of Electronics and 

Telecommunications 

Politecnico di Torino, 

10129 Torino, Italy 

Abstract—For over a decade, buffer macromodeling has been 

a topic of great interest for the integrated circuit industry. The 

performance assessment of high-speed datalinks requires 

efficient means of simulating IC ports making compact and 

accurate behavioral models valuable instruments. In the present 

communication a new modeling technique, with several 

important advantages is described. The approach is purely 

“black-box”, relying exclusively on the observation of the 

external port voltages and currents safeguarding intellectual 

property. Unlike the standard algorithms currently used in EDA 

tools, the method described in this paper models the input-output 

behavior by means of a simple nonlinear system easy to identify 

and implement. Good model performance in overclocking 

conditions is an important advantage. 

Keywords—circuit simulation; integrated circuits; nonlinear 

circuits; behavioral modeling; IBIS 

I. INTRODUCTION 

Intricate networks of interconnects exist in electronic 

systems at all levels. In this complex environment, where 

electromagnetic compatibility and signal integrity issues are 

crucial, integrated circuit (IC) buffers act as nonlinear 

dynamical terminations strongly influencing the shape of 

signals on the interconnects themselves. Simulating buffers for 

in-depth investigations is often problematic. Transistor-level 

descriptions are seldom available. They disclose the internal 

design and technology of the devices and are not normally 

released by designers. Even when provided by IC suppliers, in 

encrypted form, they lack portability, turn out to be 

computationally greedy and cannot effectively be used in a 

simulation environment. Owing to this, the best compromise is 

the development of behavioral models, that attempt to mimic 

the port electrical behavior of devices and that can be 

effectively estimated from the observation of the signals at the 

IC interfaces (see [1-4] and the references therein). The 

standard solution in IC buffer modelling is offered by the 

Input/Output Buffer Information Specification (IBIS). IBIS 

assumes simplified equivalent circuits of typical buffer 

structures and provides guidelines for collecting the key 

features of devices (e.g., the static characteristics of the output 

port of a buffer, the equivalent capacitance of the silicon die, 

the parameters of the equivalent circuit of the package,...) [5-

6]. A certain number of alternative approaches, complementing 

IBIS and providing improved accuracy for various device 

technologies are available in literature [7-14]. However, all the 

state-of-the-art approaches share common limitations. Some of 

the most common problems are related to the accurate 

reproduction of the switching behavior of devices particularly 

when dealing with overclocking operations or spurious 

transition events. In their attempt to solve these problems 

modelers have little space to maneuver. One cannot resort to 

complicated nonlinear structures that are costly in terms of 

processor cycles and maybe be difficult to implement on 

existing circuit simulators. Nor can one rely on cumbersome 

algorithms for parameter identification, flexibility is a central 

requirement. 

II. A NOTE ON THE STATE OF THE ART

Buffer circuits, such as the single-ended example 

represented in Fig. 1a, basically act as I/O interfaces between 

the internal core circuitry to the external interconnects. 

Modeling the behavior of a CMOS device of this type boils 

down to implementing a nonlinear dynamic mathematical 

relation binding the input and output port voltages to the output 

current, e.g. i Fv v 2 12 = (, ). However, most available models 

simplify the problem by first identifying a model of the output 

port, e.g. i Fv 2 2 = ( ) . A system of weighting functions is then 

used to account for the influence of the input voltage. In IBIS 

models (or models generated by similar techniques such as [7-

9]) the output current of the buffer writes: 

i t w ti v w ti v 2 22 () () ( ) () ( ) = + HH LL (1)

where i v H ( ) 2 and i v L ( )2 are the static characteristics of 

the output port of the buffer in the fixed high and low state, 

respectively, and w t H L, ( ) are weighting functions. The latter 

are computed numerically from the device responses on two 

standard loads by solving a least-squares problem (e.g., see [5] 

978-1-5090-0349-5/16/$31.00 ©2016 IEEE

Authorized licensed use limited to: University of Electronic Science and Tech of China. Downloaded on November 24,2023 at 09:26:23 UTC from IEEE Xplore. Restrictions apply. 

or [6] for details). The models are designed for easy 

implementation in SPICE with the weighting functions simply 

embedded as tables. In order for arbitrary binary signals to be 

fed into the model the weighting functions are cropped and 

pasted as up-down and down-up transitions unfold. 

The main advantage of this approach is simplicity. Model 

parameters are easily computed from both simulation and 

measured data (e.g., see [7] and [8]) and the implementation is 

robust. The main problem is related to inaccuracies, 

specifically in the case of overclocking-related phenomena. In 

the following sections we address this problem and suggest a 

different model topology that solves it, while remaining simple 

to implement and SPICE-friendly. The test device used 

throughout the paper is a Texas Instruments 8-bit bus 

transceiver (model name SN74ALVCH16973, nominal power 

supply voltage VDD = 2.5 V). Its SPICE transistor level 

description is available on the vendor's official website and was 

used to compute the reference waveforms. The power supply 

voltage is considered constant throughout the paper. 

III. A NEW PARADIGM

A. General theoretical framework 

Fig. 1. a) General structure of an IC buffer; b) Macromodel topology 

The method described in this paper is centered on the 

structure of Fig. 1b, where v1 is the input voltage, v2 is the 

output voltage, e v ˆ( )1 is the open circuit voltage response of 

the device and Gv v (, ) 1 2 is a nonlinear conductance. The 

authors were inspired by the well-known Thevenin equivalent 

model of linear circuits and imagined a similar, nonlinear 

structure, adapted for approximating the behavior of CMOS 

buffers. 

In order to simplify model extraction a reasonable 

assumption is made. Let the conductance Gv v (, ) 1 2 be written 

as a weighted sum given by equation (2). 

12 2 2 1 1

1 1

2 2

(, ) ( ) ( ) () ()

ˆ ˆ () () () 1 ()

HH LL

H L

Gv v p G v p G v v v

ev ev G v Gv

VDD VDD

        

= +

= +− (2) 

where G v H ( )2 and G v L ( ) 2 are the nonlinear conductances 

associated to the fixed high and low output state, respectively. 

The idea is to capture, by using a simple mathematical model, 

the change of the buffer's output impedance with respect to the 

variation of the input signal driving the buffer. Note that (2) 

uses e v ˆ( )1 as a weighting function in order to account for 

switching. This is consistent with the physics of CMOS 

circuits. The gate voltage intrinsically acts as a weighting 

function for the output current during switching events and is 

closely related to the open circuit-output voltage. 

Now consider Fig. 1b and equation (2). In this case, the 

expression of the output current can be cast as follows. 

2 12 1 2

12 2 12 2

12 12

( ) ( , )( ( ) ) ˆ

( )( ( ) ) ( ) ( )( ( ) ) ( ) ˆ ˆ

() ( , ) () ( , )

H HL L

H H LL

i t Gv v ev v

p t ev v G v p t ev v G v

p tf vv p tf vv

= −

= −+ −

= +

(3) 

Note the similarity between equations (1) and (3). From a 

modeler’s point of view it is reassuring to note that the top￾down approach described here (from circuit theory to 

macromodel) finally leads to a paradigm similar to the IBIS 

one obtained by a bottom-up approach (from transistor model 

to macromodel). Note, however that the only explicit link to 

the input voltage in the two submodels iH and iL of (2) is the 

presence of the fixed high and low state nominal voltages, 

VDD and 0 respectively. In the case of (3) instead, the 

inclusion of e v ˆ( )1 in fH and fL provides a stronger link to 

the input port and leads to better accuracy during switching 

events. 

With the general framework set, model parameters have to 

be determined in purely “black-box” manner. 

B. Submodel identification 

The first issue that needs to be addressed is the modeling of 

e v ˆ( )1 . It may be tempting to use a technique similar to the one 

implemented in IBIS-based EDA tools, namely, concatenated, 

properly trimmed, pre-recorded voltage curves describing 

typical up-down and down-up transitions in order to simulate 

drivers for arbitrary input signals. However, this approach, 

Authorized licensed use limited to: University of Electronic Science and Tech of China. Downloaded on November 24,2023 at 09:26:23 UTC from IEEE Xplore. Restrictions apply. 

while being robust, is also responsible for the inaccurate 

behavior observed in IBIS models used in overclocking 

conditions and the authors discarded it. Instead a parametric 

non-linear model structure of the Hammerstein type was 

chosen. It is described in Fig. 2. The open circuit voltage e v ˆ( )1

is modelled as a cascade of three blocks. The first block is 

purely static and consists in a table-based model simply 

mapping the static open-circuit input-output characteristic of 

the buffer. The second block is a linear filter and the third 

block is an ideal delay line accounting for the measured delay 

of the device in open circuit. 

Fig. 2. Submodel implementing e v ˆ( )1

The second issue concerns conductances G v H ( )2 and 

G v L ( ) 2 of (2). These are determined via a DC analysis with an 

ideal voltage source v2 connected to the output port of the 

buffer. The buffer is locked in on or off state respectively, and 

the conductances are computed via Ohm's law according to 

, G i ev v H L = − 2 12 /( ( ) ) ˆ . They are simply embedded in the 

SPICE netlist of the model as tables. It is relevant to remark 

that model parameters, i.e. eˆ and GH ,L , can also be computed 

from actual measurements performed on a real device, with 

standard solutions for the test fixture. 

C. Practical Implementation 

The extraction and implementation of the Thevenin-like 

model is summarized below. 

• Step 1. With the driver in open circuit, run a DC sweep 

of v1 in order to extract the static input-output 

characteristic v2 ( ) v1 and implement the first block in 

Fig. 2. This is a simple two column table that can be 

easily embedded in a SPICE sub-circuit. 

• Step 2. Run a transient simulation of the driver, in open 

circuit, using a step stimulus for v1 . Measure the delay 

exhibited by the v2 response and extract it. Compute 

the derivative of the un-delayed (shifted) step response 

and, subsequently, obtain the frequency response 

H j ( ) ω by simple FFT. 

• Step 3. Use vector-fitting [15], [16] to obtain a rational 

fit of the frequency response H j ( ) ω of the filter and 

generate the equivalent SPICE model. Embed the delay 

by using an ideal transmission line. At the point eˆ is 

completely modeled. 

• Step 4. Run two DC sweeps on the driver output port 

with the device locked in on and off state respectively in 

order to extract the i2 ( ) v2 characteristics. 

• Step 5. Compute GH , and GL , from the i2 ( ) v2

characteristics using Ohm's law, embed the results as 

tables in spice sub-circuit. At this point both elements of 

the Thevenin-like circuit are fully modeled. 

It’s worth noting that steps 2) and 3) could be modified to 

allow a direct identification in time domain using time-domain 

vector fitting [17]. 

IV. SIMULATION RESULTS

In this section, the Thevenin-like model is used to simulate 

a realistic interconnect structure consisting of the driver 

connected to a distributed load defined by a transmission line 

with a 60 Ohm characteristic impedance and 100 ps delay. The 

transmission line is terminated by a lumped equivalent of a 

receiver circuit represented by the shunt connection of a 7 pF 

capacitor. 

The responses of the driver, of the Thevenin-like model and 

of an IBIS model are shown in Fig. 3. The stimulus is a typical 

binary sequence including one very fast up-down-up transition 

and one very fast down-up-down transition. There is a very 

good agreement between the Thevenin response and the 

reference while the IBIS response exhibits misalignment 

phenomena each time a spurious transition occurs. 

This is not surprising, the crop-and-paste IBIS strategy has 

an intrinsic problem at high frequencies and this is precisely 

the issue that the Thevenin approach presented here addresses. 

Statistically relevant tests record an average 10× speed-up 

was for the Thevenin-like model with respect to the reference. 

The user has some leverage on the speed vs. accuracy trade-off 

by adjusting the order of the linear filter in Fig. 2. or the 

resolution of the different static mappings. This further 

confirms the strength of the proposed method and it's worth 

noting that the speed-up is independent of the complexity of 

the original transistor model. 

Fig. 3. Output port voltage response of the example buffer loading an 

transmission line with 60 Ohm characteristic impedance, 0.1 ns delay and a 

far end 7 pF capacitor. Solid black: reference response; dashed blue: Thevenin 

model prediction; dashed red: IBIS model prediction. 

V. ADDITIONAL REFINEMENTS

The main purpose of this communication was to present an 

articulated proof of concept and it was the authors’ intention to 

Authorized licensed use limited to: University of Electronic Science and Tech of China. Downloaded on November 24,2023 at 09:26:23 UTC from IEEE Xplore. Restrictions apply. 

keep the modeling framework as simple as possible. Several 

refinements and improvements may be considered in order to 

increase modeling accuracy. The first one concerns the passive 

elementGv v (, ) 1 2 . Its definition could be in order to take into 

account the dynamic behavior of the output port, mainly the 

output capacity. A “compensation capacity” may simply be 

added, for example, as in the case of IBIS models. A more 

daring approach would be to abandon the two-submodel 

hypothesis and attempt to identify Gv v (, ) 1 2 as a single 

nonlinear multiple-imput-single-output function. This strategy 

would not modify the general theoretical framework presented 

in this paper and may bring significant advantages in terms of 

accuracy. The main caveat concerns model complexity and 

robustness. 

On a more general level, the Thevenin-like model in Fig. 

1b. could be implemented in various ways. The identification 

of the e v ˆ( )1 block, for example, is a classical single-input￾single-output dynamic system identification problem and a 

plethora of methods is available in academic literature: neural 

networks, polynomial filters or various heuristics (e.g., see [18-

20]). Note though that the Hammerstein structure has the 

advantage of being simple and robust. 

Finally it would be interesting to include the variations of 

the power supply-voltage in the model. This could be achieved 

by including correction coefficients accounting for the 

variations of the power supply. 

VI. CONCLUSION

The present communication addresses the subject of IC 

buffer macromodeling and provides both a new theoretical 

framework and the methodology required to implement it. 

Implementation is, indeed, a crucial issue. The approach is 

SPICE-friendly; it can be integrated in any EDA tool just as 

easily as current mainstream techniques. All the required 

system-identification tools are off-the shelf, no cumbersome or 

exotic algorithms are required. The results presented in section 

IV demonstrate the model’s accuracy and the potential 

improvements discussed in section V should lead to further 

refinements of the technique in the near future. 

REFERENCES

[1] M. B. Yelten, T. Zhu, S. Koziel, P. D. Franzon, “Demystifying 

Surrogate Modeling for Circuits and Systems”, IEEE Circuits and 

Systems Magazine, vol. 12, no. 1, pp. 45-63, Feb. 2012. 

[2] E.-P. Li, X.-C. Wei, A. C. Cangellaris, E.-X. Liu, Y.-J. Zhang, M. 

D'Amore, J. Kim and T. Sudo, “Progress Review of Electromagnetic 

Compatibility Analysis Technologies for Packages, Printed Circuit 

Boards, and Novel Interconnects”, IEEE Trans. on Electromagnetic 

Compatibility, vol. 52, no. 2, pp. 248–265, May 2010. 

[3] M. Swaminathan, D. Chung, S. Grivet-Talocia, K. Bharath, V. Laddha 

and J. Xie, “Designing and Modeling for Power Integrity”, IEEE Trans. 

on Electromagnetic Compatibility, vol. 52, no. 2, pp. 288-310, May 

2010. 

[4] I. S. Stievano, I. A. Maio, F. G. Canavero, “Macromodeling of 

differential drivers”, IET Circuits, Devices & Systems, vol. 1, no. 1, pp. 

34-40, February, 2007 

[5] A. K. Varma, M. Steer, P.D. Franzon, “Improving Behavioral IO Buffer 

Moldeing Based on IBIS”, IEEE Transactions on Advanced Packaging, 

vol. 31, no. 4, pp. 711-721, Nov. 2008. 

[6] T. Zhu, P.D. Franzon, “Application of surrogate modeling to generate 

compact and PVT-sensitive IBIS models”, Proc. of the 18th IEEE 

Conference on Electrical Performance of Electronic Packaging and 

Systems(EPEPS), Portlant USA, pp . 77 - 80. , Oct. 2009 

[7] T. Zhu, M. B. Steer and P. D. Franzon, “Accurate and Scalable IO 

Buffer Macromodel Based on Surrogate Modeling”, IEEE Transactions 

on Components, Packaging and Manufacturing Technology, vol. 1, No. 

8, pp. 1240–1249, 2011. 

[8] I. S. Stievano, I. A. Maio, F. G. Canavero, “MΠlog, Macromodels via 

Parametric Identification of Logic Gates”, IEEE Transactions on 

Advanced Packaging, vol. 29, no. 1, pp. 102–113, Feb. 2006. 

[9] B. Mutnury, M. Swaminathan and J. P. Libous, “Macromodeling of 

nonlinear digital I/O drivers”, IEEE Transactions on Advanced 

Packaging, vol. 29, no. 1, pp. 102–113, Feb. 2006. 

[10] Y. Cao, R. Ding, Q.-J. Zhang, “State-Space Dynamic Neural Network 

Technique for High-Speed IC Applications: Modeling and Stability 

Analysis,” IEEE Transactions on Microwave Theory and Techniques, 

vol. 54, no. 6, pp. 2398–2409, June 2006. 

[11] Y. Cao, Q.-J. Zhang, “A New Training Approach for Robust Recurrent 

Neural-Network Modeling of Nonlinear Circuits,” IEEE Transactions on 

Microwave Theory and Techniques, vol. 57, no. 6, pp. 1539–1553, June 

2009. 

[12] W. Dghais, T. R. Cunha, J. C. Pedro, “A Novel Two-Port Behavioral 

Model for I/O Buffer Overclocking Simulation”, IEEE Trans. Compon., 

Packag. Manuf. Technol., vol. 3, no. 10, October 2013 

[13] W. Dghais, T. R. Cunha, J. C. Pedro, “Reduced-order parametric 

Behavioral model for digital buffers/drivers with physical support”,IEEE 

Trans. Compon., Packag. Manuf. Technol., vol. 2, no. 12, pp. 110, Dec. 

2012. 

[14] Y. Cao, I. Erdin, Q.-J. Zhang, “Transient Behavioral Modeling of 

Nonlinear I/O Drivers Combining Neural Networks and Equivalent 

Circuits”, IEEE Microwave and Wireless Components Letters, vol. 20, 

no.12, pp: 645-647, Nov. 2010. 

[15] B. Gustavsen and A. Semlyen, “Rational approximation of frequency 

domain responses by vector fitting”, IEEE Trans. Power Delivery, vol. 

14, no. 3, pp. 1052-1061, July 1999 

[16] B. Gustavsen, “Relaxed Vector Fitting Algorithm for Rational 

Approximation of Frequency Domain Responses”, Proc. of the 10th 

IEEE Workshop on Signal Propagation on Interconnects (SPI), Berlin, 

Germany, pp.97-100, May 2006. 

[17] S. Grivet-Talocia, “Package Macromodeling via Time-Domain Vector 

Fitting”, IEEE Microw. and Wireless Comp. Lett., vol. 13, no. 11, pp. 

472-474, November 2003. 

[18] S. Haykin, Neural Networks - A Comprehensive Foundation. 

Englewood Cliffs, NJ: Prentice Hall, 1999. 

[19] M. G. Telescu, I. S. Stievano, F. G. Canavero, N. Tanguy, “An 

Application of Volterra Series to IC Buffer Models”, Proc. of the 14th 

IEEE Workshop on Signal Propagation on Interconnects (SPI), 

Hildesheim, Germany, pp. 93-96, May 2010. 

[20] C. Diouf, M. Telescu, N. Tanguy, P. Cloastre, I. S Stievano, F. G 

Canavero, “Statically constrained nonlinear models with application to 

IC buffers”, Proc. of the 15th IEEE Workshop on Signal Propagation on 

Interconnects (SPI), Naples, Italy, pp.115-118, May 2011. 

Authorized licensed use limited to: University of Electronic Science and Tech of China. Downloaded on November 24,2023 at 09:26:23 UTC from IEEE Xplore. Restrictions apply. 

