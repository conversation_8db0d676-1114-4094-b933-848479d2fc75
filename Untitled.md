### 1.数字信号处理的基本概念

​	一般来说，数字信号处理的对象是数字信号，模拟信号处理的对象是模拟信号。但是，如果系统中增加数/模转换器和模/数转换器，那么,数字信号处理系统也可以处理模拟信号。这里关键的问题是两种信号处理系统对信号处理的方式不同，数字信号处理是采用数值计算的方法完成对信号的处理，而模拟信号处理则是通过一些模拟器件(例如晶体管、运算放大器、电阻、电容、电感等)组成的网络来完成对信号的处理。例如，图0.0.1(a)所示的是一个简单的模拟高通滤波器，它是由电阻R和电容C组成的，而图0.0.1(b)所示的则是一个简单的数字高通滤波器，它是由一个加法器、一个乘法器和一个延时器组成的。因此，简单地说，数字信号处理就是用数值计算的方法对信号进行处理，这里“处理”的实质是“运算”,处理对象则包括模拟信号和数字信号。

![image-20250527175708893](https://raw.githubusercontent.com/jaynerlin/note_picture/master/20250527175708920.png)

### 2.数字信号处理的实现方法

​	数字信号处理的主要对象是数字信号，且是采用数值运算的方法达到处理目的的。因此，其实现方法不同于模拟信号处理的实现方法。数字信号处理的实现方法基本上可以分成两种，即软件实现方法和硬件实现方法。软件实现方法指的是按照原理和算法，自己编写程序或者采用现成的程序在通用计算机上实现；硬件实现是按照具体的要求和算法，设计硬件结构图，用乘法器、加法器、延时器、控制器、存储器以及输入输出接口等基本部件实现的一种方法。显然，软件实现灵活，只要改变程序中的有关参数，例如只要改变图0.0.1(b)中的参数a,数字滤波器可能就是低通、带通或高通滤波器，但是运算速度慢，一般达不到实时处理，因此，这种方法适合于算法研究和仿真。硬件实现运算速度快，可以达到实时处理要求，但是不灵活。

​	用单片机实现的方法属于软硬结合实现，现在单片机发展很快，功能也很强，配以数字信号处理软件，既灵活，速度又比软件方法快。采用专用的数字信号处理芯片(DSP芯片)是目前发展最快、应用最广的一种方法。因为DSP芯片比通用单片机有更为突出的优点，它结合了数字信号处理的特点，内部配有乘法器和累加器，结构上采用了流水线工作方式以及并行结构、多总线，且配有适合数字信号处理的指令，是一类可实现高速运算的微处理器。DSP芯片已由最初的8位发展为16位、32位，且性能优良的高速DSP不断面市，价格也在不断下降。可以说，用DSP芯片实现数字信号处理，正在变成或已经变成工程技术领域中的主要实现方法。

​	综上所述，如果从数字信号处理的实际应用情况和发展考虑，数字信号处理的实现方法分成软件实现和硬件实现两大类。而硬件实现指的是选用合适的DSP芯片，配有适合芯片语言及任务要求的软件，实现某种信号处理功能的一种方法。这种系统无疑是一种最佳的数字信号处理系统。对于更高速的实时系统，DSP的速度也不满足要求时，应采用可编程超大规模器件或开发专用芯片来实现。

### 3.数字信号处理的特点

由于数字信号处理是用数值运算的方式实现对信号的处理的，因此，相对模拟信号处理，数字信号处理主要有以下优点：

1) 灵活性

​	数字信号处理系统(简称数字系统)的性能取决于系统参数，这些参数存储在存储器中，很容易改变，因此系统的性能容易改变，甚至通过参数的改变，系统可以变成各种完全不同的系统。灵活性还表现在数字系统可以时分复用，用一套数字系统分时处理几路信号。数字系统可以实现智能系统的功能，可以根据环境条件、用户需求，自动选择最佳的处理算法，例如，软件无线电等。软件无线电的基本思想就是：将宽带A/D变换器及D/A变换器尽可能地靠近射频天线，建立一个具有“A/D-DSP-D/A”模型的通用的、开放的硬件平台，在这个硬件平台上尽量利用软件技术来实现电台的各种功能模块。例如，通过可编程数字滤波器对信道进行分离；使用数字信号处理器(DSP)技术，通过软件编程来实现通信频段的选择以及完成传送信息抽样、量化、编码/解码、运算处理和变换等；通过软件编程实现不同的信道调制方式的选择，如调幅、调频、单边带、跳频和扩频等；通过软件编程实现不同的保密结构、网络协议和控制终端功能等。

2) 高精度和高稳定性

​	数字系统的特性不易随使用条件变化而变化，尤其使用了超大规模集成的DSP芯片，使设备简化，进一步提高了系统的稳定性和可靠性。运算位数又由8位提高到16、32位，在计算精度方面，模拟系统是不能和数字系统相比拟的，为此，许多测量仪器为满足高精度的要求只能采用数字系统。

3) 便于大规模集成

​	数字部件具有高度的规范性，对电路元件参数要求不严，容易大规模集成和大规模生产，价格不断降低，这也是DSP芯片和超大规模可编程器件发展迅速的主要因素之一。由于采用了大规模集成电路，数字系统体积小、重量轻、可靠性强。

4)可以实现模拟系统无法实现的诸多功能

​	数字信号可以存储，数字系统可以进行各种复杂的变换和运算。这一优点更加使数字信号处理不再仅仅限于对模拟系统的逼近，它可以实现模拟系统无法实现的诸多功能。例如，电视系统中的画中画、多画面以及各种视频特技，包括画面压缩、画面放大、画面坐标旋转、演员特技制作；变声变调的特殊的配音制作；解卷积；图像信号的压缩编码；高级加密解密；数字滤波器严格的线性相位特性；等等。

